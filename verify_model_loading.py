#!/usr/bin/env python3
"""
验证模型加载是否正确
"""

import os
import torch

def verify_model_loading():
    """验证模型加载"""
    
    print("=== 验证模型加载 ===")
    
    try:
        # 检查模型文件
        model_path = "models/panel_detection/panel_detection.pth"
        backup_path = "models/panel_detection/panel_detection_backup.pth"
        gpu_optimized_path = "models/panel_detection/gpu_optimized_panel_detection.pth"
        
        print(f"📁 模型文件检查:")
        print(f"  - 默认模型: {'✅' if os.path.exists(model_path) else '❌'} {model_path}")
        print(f"  - 备份模型: {'✅' if os.path.exists(backup_path) else '❌'} {backup_path}")
        print(f"  - GPU优化模型: {'✅' if os.path.exists(gpu_optimized_path) else '❌'} {gpu_optimized_path}")
        
        if os.path.exists(model_path):
            model_size = os.path.getsize(model_path) / (1024 * 1024)
            print(f"  - 默认模型大小: {model_size:.1f} MB")
        
        # 直接加载模型检查内容
        print(f"\n🔍 直接检查模型内容:")
        if os.path.exists(model_path):
            checkpoint = torch.load(model_path, map_location='cpu')
            print(f"  - 模型键: {list(checkpoint.keys())}")
            
            if 'model' in checkpoint:
                model_state = checkpoint['model']
                print(f"  - 模型参数数量: {len(model_state)}")
                
                # 检查一些关键参数
                key_params = [
                    'roi_heads.box_predictor.cls_score.weight',
                    'roi_heads.box_predictor.cls_score.bias',
                    'roi_heads.box_predictor.bbox_pred.weight',
                    'roi_heads.box_predictor.bbox_pred.bias'
                ]
                
                print(f"  - 关键参数检查:")
                for param in key_params:
                    if param in model_state:
                        shape = model_state[param].shape
                        print(f"    ✅ {param}: {shape}")
                    else:
                        print(f"    ❌ {param}: 不存在")
            
            if 'iteration' in checkpoint:
                print(f"  - 训练迭代数: {checkpoint['iteration']}")
            
            if 'scheduler' in checkpoint:
                print(f"  - 调度器状态: 存在")
        
        # 测试模型加载
        print(f"\n🧪 测试模型加载:")
        from manga_translator.detection.deep_panel_detector import DeepPanelDetector
        
        # 强制使用我们的模型路径
        detector = DeepPanelDetector(model_path=model_path, device="cpu")
        success = detector.initialize()
        
        print(f"  - 初始化结果: {'成功' if success else '失败'}")
        
        if success:
            print(f"  - 模型路径: {detector.model_path}")
            print(f"  - 配置名称: {detector.config_name}")
            print(f"  - 设备: {detector.device}")
            
            # 获取模型信息
            model_info = detector.get_model_info()
            print(f"  - 模型信息: {model_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_model_loading()
